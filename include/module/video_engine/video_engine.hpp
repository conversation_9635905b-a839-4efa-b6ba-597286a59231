// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MODULE__VIDEO_ENGINE_HPP_
#define MODULE__VIDEO_ENGINE_HPP_

#include "common/base_module.hpp"
#include "common/topics/sensor_light.hpp"
#include "common/topics/cat_event.hpp"
#include "utils/api_client.h"
#include <atomic>
#include <mutex>
#include <thread>
#include <deque>
#include <algorithm>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <unistd.h>
#include <json-c/json.h>

// Dark mode ioctl definitions
#define DARK_IOC_MAGIC 'd'
#define DARK_SET_HIGH _IOWR(DARK_IOC_MAGIC, 1, uint8_t)  // 读写操作：设置GPIO为高电平并返回实际状态
#define DARK_SET_LOW _IOWR(DARK_IOC_MAGIC, 2, uint8_t)   // 读写操作：设置GPIO为低电平并返回实际状态
#define DARK_GET_MODE _IOR(DARK_IOC_MAGIC, 3, uint8_t)   // 读取操作：获取当前GPIO状态

#define DEVICE_PATH "/dev/dark"

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/avutil.h>
#include <libswresample/swresample.h>
#include <libswscale/swscale.h>
}

// 前向声明launch_main函数
extern int launch_main();

namespace aby_box {

// 光照数据对结构
struct LightDataPair {
    uint16_t ch0;  // 可见光通道数据
    uint16_t ch1;  // 红外通道数据

    LightDataPair(uint16_t c0 = 0, uint16_t c1 = 0) : ch0(c0), ch1(c1) {}
};

// 阈值配置结构
struct LightThresholds {
    LightDataPair rgb_to_gray;  // RGB模式进入灰度模式的阈值
    LightDataPair gray_to_rgb;  // 灰度模式进入RGB模式的阈值

    LightThresholds() : rgb_to_gray(100, 100), gray_to_rgb(4000, 1000) {}
};

// 摄像头错误类型枚举
enum class CameraErrorType {
  NONE = 0,
  INITIALIZATION_FAILED,
  SENSOR_DISCONNECTED,
  API_CALL_FAILED,
  MEMORY_ERROR,
  THREAD_CRASHED,
  UNKNOWN_ERROR
};

class VideoEngine : public BaseModule {
public:
  explicit VideoEngine(const std::string &module_name)
      : BaseModule(module_name), is_running_(false), 
        camera_failed_(false) {}
  ~VideoEngine() {}
  bool init() override;
  bool start() override;
  bool stop() override;
  void join() override;

  // 错误处理相关方法
  void reportCameraError(CameraErrorType error_type, const std::string& error_msg);
  bool isCameraHealthy() const;

  // 视频模式控制方法
  bool isGrayModeEnabled() const { return gray_mode_enabled_.load(); }

private:
  void recording_loop();
  void logCameraError(CameraErrorType error_type, const std::string& error_msg);
  void light_analysis_loop();  // 光照分析和视频模式控制线程

  // 光照分析相关方法
  bool load_light_thresholds();
  bool initialize_video_mode_state();
  bool get_current_gpio_state();
  bool verify_camera_mode_switch(uint8_t target_mode);
  uint16_t calculate_median(const std::vector<uint16_t>& data);
  bool has_jump_variation(const std::deque<LightDataPair>& window);
  bool should_switch_mode(const std::deque<LightDataPair>& window);
  bool set_video_mode(uint8_t mode);

  std::atomic<bool> is_running_;
  std::thread recording_thread_;
  std::thread light_analysis_thread_;  // 光照分析和视频模式控制线程

  // 错误处理相关成员
  std::atomic<bool> camera_failed_;  // 标记摄像头是否已失效
  std::mutex error_mutex_;
  std::string last_error_message_;

  // 视频模式控制相关成员
  std::atomic<bool> gray_mode_enabled_{false};  // 是否启用灰度模式
  std::mutex video_mode_mutex_;  // 保护视频模式切换的互斥锁

  // 光照分析相关成员
  const std::string config_file_path_ = "/etc/cfg/aby_box/lighting.json";
  LightThresholds light_thresholds_;
  std::atomic<uint8_t> current_video_mode_{0};  // 0=COLOR, 1=GRAY
  std::deque<LightDataPair> sliding_window_;
  std::atomic<bool> cat_leave_detected_{true};  // 初始化为true，允许光照分析
  std::chrono::steady_clock::time_point last_mode_switch_time_;  // 上次模式切换时间

  // 常量定义
  static constexpr size_t WINDOW_SIZE = 20;
  static constexpr uint16_t JUMP_THRESHOLD = 100;
  static constexpr size_t COUNT_THRESHOLD = 8;
  static constexpr std::chrono::seconds MODE_SWITCH_COOLDOWN{10};  // 模式切换冷却时间10秒
};
} // namespace aby_box

#endif // VIDEO_ENGINE_HPP_