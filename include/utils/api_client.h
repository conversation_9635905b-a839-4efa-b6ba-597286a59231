#ifndef API_CLIENT_H
#define API_CLIENT_H

#include "utils/logging.hpp"
// Forward declaration for OtaManager to avoid circular includes
namespace aby_box {
class OtaManager;
}

#include <atomic>
#include <chrono>
#include <curl/curl.h>
#include <memory>
#include <nlohmann/json.hpp>
#include <string>
#include <thread>

namespace aby_box {

// Common callback for CURL operations
static size_t WriteCallback(void *contents, size_t size, size_t nmemb,
                            std::string *userp);

// System command execution helper
static int executeCommand(const std::string &cmd);

class APIClient {
public:
  static APIClient &getInstance();

  // Delete copy constructor and assignment operator
  APIClient(const APIClient &) = delete;
  APIClient &operator=(const APIClient &) = delete;

  bool init();
  void cleanup();

  bool startRecording(uint64_t timestamp);
  bool stopRecording();
  bool isRecording() const { return is_recording_; }
  
  // 获取当前录制的时间戳，确保与录制视频使用相同的时间戳
  uint64_t getCurrentRecordingTimestamp() const { return current_recording_timestamp_.load(); }

  // 实时获取配置文件中的设备信息，确保获取到最新值
  std::string get_device_id() const;
  std::string get_user_id() const;
  std::string get_hardware_sn() const;
  void set_device_id(const std::string &id) { device_id_ = id; }
  
  /**
   * 设置用户ID并更新配置文件
   * @param id 新的用户ID
   * @return 是否成功更新配置文件
   */
  
  void setBaseUrl(const std::string &url) { base_url_ = url; }

  std::string get_ip_timezone();
  /**
   * Sends device timezone to the server
   *
   * @param timezone The timezone string (e.g., "Asia/Shanghai")
   * @return true if timezone was sent successfully, false otherwise
   */
  bool send_device_timezone(const std::string &timezone);

  /**
   * Creates a video record on the server with automatically generated video ID
   *
   * @param weight_litter The weight of the litter
   * @param weight_cat The weight of the cat
   * @param weight_waste The weight of the waste
   * @return true if the record was created successfully, false otherwise
   */
  bool createVideoRecord(int64_t timestamp_enter, int64_t timestamp_leave,
                         double weight_litter = 0.0, double weight_cat = 0.0,
                         double weight_waste = 0.0);

  /**
   * Sends a heartbeat to the server and retrieves system status information
   * 
   * @return JSON response containing system status information including OTA update availability
   */
  nlohmann::json sendHeartbeat();

  /**
   * Updates the OTA status for this device on the server
   * 
   * @param status The OTA status: "idle", "updating", "failed", or "completed"
   * @return true if the status was updated successfully, false otherwise
   */
  bool updateOtaStatus(const std::string &status);

  /**
   * Gets user ID by hardware serial number from the server
   * 
   * @param hardware_sn The hardware serial number
   * @return user ID string if successful, empty string if failed
   */
  std::string getUserIdByHardwareSn(const std::string &hardware_sn);

  /**
   * Reports sensor error status to the server
   * 
   * @param sensor_type The type of sensor ("camera", "weight_sensor", "temperature_humidity_sensor", "microphone", "wifi", "bluetooth")
   * @param error_code The error code (optional, 0 if not specified)
   * @param error_message The error message (optional, empty if not specified)
   * @param additional_info Additional error information (optional, empty if not specified)
   * @return true if the sensor error was reported successfully, false otherwise
   */
  bool reportSensorError(const std::string &sensor_type, 
                        int error_code = 0, 
                        const std::string &error_message = "",
                        const std::string &additional_info = "");

  /**
   * Clears sensor error status on the server using DELETE method
   * 
   * @param sensor_type The type of sensor ("camera", "weight_sensor", "temperature_humidity_sensor", "microphone", "wifi", "bluetooth")
   * @return true if the sensor error was cleared successfully, false otherwise
   */
  bool clearSensorError(const std::string &sensor_type);

  // Set OTA manager reference for status checking
  void setOtaManager(std::shared_ptr<OtaManager> ota_manager) { ota_manager_ = ota_manager; }
  CURLcode performSimpleGetRequest(const std::string &url,
                                   std::string &response);
  CURLcode performCurlRequest(const std::string &url, const std::string &method,
                              const std::string &postData,
                              std::string &response, long *http_code = nullptr);

private:
  APIClient(); // Private constructor
  ~APIClient() = default;

  // Constants
  static constexpr int MAX_RETRY_ATTEMPTS = 3;
  static constexpr int RETRY_DELAY_MS = 200;
  static constexpr uint64_t STOP_DELAY_MS = 100;
  static constexpr int NETWORK_CHECK_INTERVAL_MS =
      10000; // 10 seconds check interval

  // Member variables
  std::string base_url_;
  std::shared_ptr<LogHandler> log_handler_;
  std::atomic<bool> is_recording_{false};
  std::atomic<bool> stop_pending_{false};
  std::atomic<uint64_t> stop_request_time_{0};
  std::atomic<uint64_t> current_recording_timestamp_{0}; // 当前录制的时间戳
  std::thread stop_thread_;
  std::atomic<bool> network_ready_{false};
  std::atomic<bool> should_check_network_{true};
  std::string device_id_;
  std::string user_id_;
  std::string hardware_sn_;
  std::atomic<bool> should_exit_{false};

  // 录制超时控制相关
  std::atomic<uint64_t> recording_start_time_{0}; // 录制开始时间戳
  std::thread recording_timeout_thread_;
  std::atomic<bool> timeout_thread_running_{false};
  static constexpr uint64_t MAX_RECORDING_DURATION_MS = 20 * 60 * 1000; // 20分钟

  // OTA manager reference
  std::shared_ptr<OtaManager> ota_manager_;

  // Helper methods declarations
  std::string readFromFile(const std::string &path);
  void loadConfigValues();

  // 录制超时控制方法
  void startRecordingTimeoutMonitor();
  void stopRecordingTimeoutMonitor();
  void recordingTimeoutLoop();
};

} // namespace aby_box
#endif // API_CLIENT_H