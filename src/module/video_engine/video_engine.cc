#include "module/video_engine/video_engine.hpp"
#include "module/video_engine/mpi/video_detector_engine.h"
#include "utils/api_client.h"
#include "uorb/subscription.h"
#include "uorb/abs_time.h"
#include <ctime>
#include <sstream>
#include <stdexcept>
#include <csignal>
#include <cmath>

// 全局变量，用于跨文件的线程间通信
std::atomic<aby_box::VideoEngine*> g_video_engine_instance{nullptr};

// 信号处理函数
static void camera_crash_handler(int sig) {
  // 防止重复处理相同信号
  static std::atomic<bool> signal_being_handled{false};
  static std::atomic<int> last_signal{0};
  static std::atomic<time_t> last_signal_time{0};
  
  time_t now = time(nullptr);
  time_t time_since_last = now - last_signal_time.load();
  
  // 如果相同信号在10秒内重复触发，忽略后续信号
  if (signal_being_handled.load() || 
      (last_signal.load() == sig && time_since_last < 10)) {
    return;
  }
  
  signal_being_handled.store(true);
  last_signal.store(sig);
  last_signal_time.store(now);
  
  if (auto* engine = g_video_engine_instance.load()) {
    engine->reportCameraError(aby_box::CameraErrorType::THREAD_CRASHED, 
                              "Camera thread crashed with signal: " + std::to_string(sig));
  }
  
  signal_being_handled.store(false);
}

namespace aby_box {

bool VideoEngine::init() {
  log_handler_->info("Initializing VideoEngine with crash detection");

  // 重置全局退出标志
  bExit = false;

  // 注册全局实例
  g_video_engine_instance.store(this);

  // 安装信号处理器
  signal(SIGSEGV, camera_crash_handler);
  signal(SIGABRT, camera_crash_handler);
  signal(SIGFPE, camera_crash_handler);

  // 加载光照阈值配置
  if (!load_light_thresholds()) {
    log_handler_->error("Failed to load light thresholds from config file");
    // 使用默认值继续运行
  }

  // 获取当前视频模式并确保相机和GPIO状态一致
  if (!initialize_video_mode_state()) {
    log_handler_->error("Failed to initialize video mode state, defaulting to COLOR mode");
    current_video_mode_.store(0); // COLOR mode
    gray_mode_enabled_.store(false);
  }

  // 初始化模式切换时间戳
  last_mode_switch_time_ = std::chrono::steady_clock::now() - MODE_SWITCH_COOLDOWN;

  camera_failed_.store(false);
  log_handler_->info("VideoEngine initialized - camera errors will cause immediate shutdown");
  log_handler_->info("RGB to Gray thresholds: ch0={}, ch1={}",
                    light_thresholds_.rgb_to_gray.ch0, light_thresholds_.rgb_to_gray.ch1);
  log_handler_->info("Gray to RGB thresholds: ch0={}, ch1={}",
                    light_thresholds_.gray_to_rgb.ch0, light_thresholds_.gray_to_rgb.ch1);
  return true;
}

bool VideoEngine::start() {
  log_handler_->info("Starting VideoEngine");

  if (is_running_.load()) {
    log_handler_->warn("VideoEngine already running");
    return true;
  }

  is_running_ = true;

  // 启动录制线程
  recording_thread_ = std::thread(&VideoEngine::recording_loop, this);
  pthread_setname_np(recording_thread_.native_handle(), "aby_vid_recording");

  // 启动光照分析和视频模式控制线程
  light_analysis_thread_ = std::thread(&VideoEngine::light_analysis_loop, this);
  pthread_setname_np(light_analysis_thread_.native_handle(), "aby_light_analysis");

  log_handler_->info("VideoEngine started successfully");
  return true;
}

bool VideoEngine::stop() {
  log_handler_->info("Stopping VideoEngine");
  is_running_ = false;

  // 不再设置全局退出标志，避免影响其他模块
  // bExit = true;

  // 清除全局实例
  g_video_engine_instance.store(nullptr);

  return true;
}

void VideoEngine::join() {
  if (recording_thread_.joinable()) {
    recording_thread_.join();
  }
  if (light_analysis_thread_.joinable()) {
    light_analysis_thread_.join();
  }
  log_handler_->info("VideoEngine threads joined");
}

void VideoEngine::recording_loop() {
  log_handler_->info("VideoEngine recording loop started");
  
  try {
    // launch_main 应该持续运行，正常情况下不会返回
    // 如果返回了，无论返回值是什么，都说明出现了问题
    log_handler_->info("Starting camera main function");
    int result = launch_main();
    
    // 如果我们到达这里，说明launch_main退出了
    if (result == 0) {
      log_handler_->warn("launch_main exited normally, this indicates a problem");
      reportCameraError(CameraErrorType::UNKNOWN_ERROR, 
                       "launch_main unexpectedly exited with success code");
    } else {
      log_handler_->error("launch_main exited with error code: {} (likely VI init failed)", result);
      if (result == -1) {
        // launch_main返回-1通常表示VI初始化失败
        reportCameraError(CameraErrorType::INITIALIZATION_FAILED, 
                         "Camera initialization failed (VI init failed)");
      } else {
        reportCameraError(CameraErrorType::API_CALL_FAILED, 
                         "launch_main returned error code: " + std::to_string(result));
      }
    }
    
  } catch (const std::exception& e) {
    reportCameraError(CameraErrorType::UNKNOWN_ERROR, 
                     std::string("Exception in recording loop: ") + e.what());
  } catch (...) {
    reportCameraError(CameraErrorType::UNKNOWN_ERROR, 
                     "Unknown exception in recording loop");
  }
  
  log_handler_->info("VideoEngine recording loop exited");
}

void VideoEngine::reportCameraError(CameraErrorType error_type, const std::string& error_msg) {
  std::lock_guard<std::mutex> lock(error_mutex_);
  
  // 防止重复报告
  if (camera_failed_.load()) {
    return;
  }
  
  camera_failed_.store(true);
  last_error_message_ = error_msg;
  
  logCameraError(error_type, error_msg);
  
  // 立即发送传感器错误状态到服务器
  std::thread([this, error_type, error_msg]() {
    try {
      // 构造错误码和错误消息（限制长度以符合数据库列限制）
      int error_code = static_cast<int>(error_type);
      std::string error_message = error_msg;
      if (error_message.length() > 50) {
        error_message = error_message.substr(0, 47) + "...";
      }
      
      // 根据错误类型添加额外信息（缩短字符串以符合数据库列长度限制）
      std::string additional_info;
      switch (error_type) {
        case CameraErrorType::INITIALIZATION_FAILED:
          additional_info = "Init failed";
          break;
        case CameraErrorType::SENSOR_DISCONNECTED:
          additional_info = "Sensor disconnected";
          break;
        case CameraErrorType::API_CALL_FAILED:
          additional_info = "API call failed";
          break;
        case CameraErrorType::MEMORY_ERROR:
          additional_info = "Memory error";
          break;
        case CameraErrorType::THREAD_CRASHED:
          additional_info = "Thread crashed";
          break;
        case CameraErrorType::UNKNOWN_ERROR:
          additional_info = "Unknown error";
          break;
        default:
          additional_info = "Unspecified error";
          break;
      }
      
      // 调用API上报传感器错误
      bool success = APIClient::getInstance().reportSensorError(
        "camera", error_code, error_message, additional_info);
      
      if (success) {
        log_handler_->info("Camera error successfully reported to server");
      } else {
        log_handler_->error("Failed to report camera error to server");
      }
    } catch (const std::exception& e) {
      log_handler_->error("Exception while reporting camera error to server: {}", e.what());
    } catch (...) {
      log_handler_->error("Unknown exception while reporting camera error to server");
    }
  }).detach(); // 使用detach避免阻塞主线程
  
  log_handler_->error("Camera failure detected - stopping VideoEngine module immediately");

  // 不再设置全局退出标志，只停止VideoEngine模块
  // bExit = true;

  // 直接停止video_engine模块
  is_running_.store(false);
}

bool VideoEngine::isCameraHealthy() const {
  return !camera_failed_.load();
}

void VideoEngine::logCameraError(CameraErrorType error_type, const std::string& error_msg) {
  std::string error_type_str;
  
  switch (error_type) {
    case CameraErrorType::NONE:
      error_type_str = "NONE";
      break;
    case CameraErrorType::INITIALIZATION_FAILED:
      error_type_str = "INITIALIZATION_FAILED";
      break;
    case CameraErrorType::SENSOR_DISCONNECTED:
      error_type_str = "SENSOR_DISCONNECTED";
      break;
    case CameraErrorType::API_CALL_FAILED:
      error_type_str = "API_CALL_FAILED";
      break;
    case CameraErrorType::MEMORY_ERROR:
      error_type_str = "MEMORY_ERROR";
      break;
    case CameraErrorType::THREAD_CRASHED:
      error_type_str = "THREAD_CRASHED";
      break;
    case CameraErrorType::UNKNOWN_ERROR:
      error_type_str = "UNKNOWN_ERROR";
      break;
  }
  
  log_handler_->error("Camera Error [{}]: {}", error_type_str, error_msg);
}

void VideoEngine::light_analysis_loop() {
    uorb::SubscriptionData<uorb::msg::sensor_light> sub_sensor_light;
    uorb::SubscriptionData<uorb::msg::cat_event> sub_cat_event;

    int timeout_ms = 1000; // 1000ms timeout for polling

    struct orb_pollfd poll_fds[] = {
        {.fd = sub_sensor_light.handle(), .events = POLLIN, .revents = 0},
        {.fd = sub_cat_event.handle(), .events = POLLIN, .revents = 0}
    };

    log_handler_->info("Light analysis loop started");

    while (is_running_.load()) {
        // Poll for new messages
        int poll_ret = orb_poll(poll_fds, 2, timeout_ms);

        if (poll_ret < 0) {
            log_handler_->error("Poll error in light analysis");
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }

        // Check for cat event updates
        if (poll_fds[1].revents & POLLIN) {
            if (sub_cat_event.Update()) {
                auto event = sub_cat_event.get();
                if (event.event_type == 1) { // CAT_LEAVE
                    cat_leave_detected_ = true;
                    log_handler_->debug("CAT_LEAVE event detected, enabling analysis");
                } else if (event.event_type == 0) { // CAT_ENTER
                    cat_leave_detected_ = false;
                    log_handler_->debug("CAT_ENTER event detected, disabling analysis");
                }
            }
        }

        // Check for sensor light updates
        if (poll_fds[0].revents & POLLIN) {
            if (sub_sensor_light.Update()) {
                auto light_data = sub_sensor_light.get();

                // 添加数据到滑动窗口
                LightDataPair data_pair(light_data.light_visible, light_data.light_ir);
                sliding_window_.push_back(data_pair);

                // 保持窗口大小为20
                if (sliding_window_.size() > WINDOW_SIZE) {
                    sliding_window_.pop_front();
                }

                // 只有在CAT_LEAVE状态下才进行分析判断
                if (cat_leave_detected_ && sliding_window_.size() == WINDOW_SIZE) {
                    // log_handler_->debug("Checking if mode switch needed: window_size={}, current_mode={}",
                    //                    sliding_window_.size(), current_video_mode_.load());
                    if (should_switch_mode(sliding_window_)) {
                        // 需要切换模式
                        uint8_t target_mode = (current_video_mode_.load() == 0) ? 1 : 0; // 0=COLOR, 1=GRAY
                        // log_handler_->info("Mode switch triggered: current={} -> target={}",
                        //                   current_video_mode_.load(), target_mode);
                        set_video_mode(target_mode);
                    }
                }
                // } else if (sliding_window_.size() == WINDOW_SIZE) {
                //     log_handler_->debug("Skipping analysis: cat_leave={}, window_size={}",
                //                        cat_leave_detected_.load(), sliding_window_.size());
                // }

                // 调试日志（每100个样本打印一次）
                // static int sample_count = 0;
                // if (++sample_count % 100 == 0) {
                //     log_handler_->debug("Light data: ch0={}, ch1={}, window_size={}, cat_leave={}, current_mode={}",
                //                       light_data.light_visible, light_data.light_ir,
                //                       sliding_window_.size(), cat_leave_detected_.load(),
                //                       current_video_mode_.load());
                // }
            }
        }

        // 如果没有事件，短暂休眠
        if (poll_ret == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }

    log_handler_->info("Light analysis loop stopped");
}

bool VideoEngine::load_light_thresholds() {
    try {
        json_object *root = json_object_from_file(config_file_path_.c_str());
        if (!root) {
            log_handler_->error("Failed to open config file: {}", config_file_path_);
            return false;
        }

        json_object *thresholds_obj;
        if (!json_object_object_get_ex(root, "thresholds", &thresholds_obj)) {
            log_handler_->error("Missing 'thresholds' section in config file");
            json_object_put(root);
            return false;
        }

        // 读取RGB到灰度模式的阈值
        json_object *rgb_to_gray_obj;
        if (json_object_object_get_ex(thresholds_obj, "rgb_to_gray", &rgb_to_gray_obj)) {
            json_object *ch0_obj, *ch1_obj;
            if (json_object_object_get_ex(rgb_to_gray_obj, "ch0", &ch0_obj)) {
                light_thresholds_.rgb_to_gray.ch0 = json_object_get_int(ch0_obj);
            }
            if (json_object_object_get_ex(rgb_to_gray_obj, "ch1", &ch1_obj)) {
                light_thresholds_.rgb_to_gray.ch1 = json_object_get_int(ch1_obj);
            }
        }

        // 读取灰度到RGB模式的阈值
        json_object *gray_to_rgb_obj;
        if (json_object_object_get_ex(thresholds_obj, "gray_to_rgb", &gray_to_rgb_obj)) {
            json_object *ch0_obj, *ch1_obj;
            if (json_object_object_get_ex(gray_to_rgb_obj, "ch0", &ch0_obj)) {
                light_thresholds_.gray_to_rgb.ch0 = json_object_get_int(ch0_obj);
            }
            if (json_object_object_get_ex(gray_to_rgb_obj, "ch1", &ch1_obj)) {
                light_thresholds_.gray_to_rgb.ch1 = json_object_get_int(ch1_obj);
            }
        }

        json_object_put(root);
        return true;
    } catch (const std::exception &e) {
        log_handler_->error("Exception while loading thresholds: {}", e.what());
        return false;
    }
}

bool VideoEngine::initialize_video_mode_state() {
    log_handler_->info("Initializing video mode state - checking camera and GPIO consistency");

    // 1. 获取当前GPIO状态
    int fd = open(DEVICE_PATH, O_RDWR);
    if (fd < 0) {
        log_handler_->error("Failed to open dark mode device during initialization: {}", DEVICE_PATH);
        return false;
    }

    uint8_t gpio_state = 0;
    int ioctl_ret = ioctl(fd, DARK_GET_MODE, &gpio_state);
    close(fd);

    if (ioctl_ret < 0) {
        log_handler_->error("Failed to get current GPIO state during initialization");
        return false;
    }

    // 2. 从GPIO状态推断应该的相机模式
    uint8_t expected_camera_mode = (gpio_state == 1) ? 1 : 0; // 1=GRAY, 0=COLOR

    // 3. 设置内部状态与GPIO保持一致
    current_video_mode_.store(expected_camera_mode);
    gray_mode_enabled_.store(expected_camera_mode == 1);

    log_handler_->info("Initialized video mode state: GPIO={}, Camera Mode={}, Gray Enabled={}",
                      gpio_state, expected_camera_mode, gray_mode_enabled_.load());

    // 4. 验证相机是否按预期工作（这里我们假设相机会根据gray_mode_enabled_标志工作）
    // 实际的相机模式切换在video_detector_engine.cc中通过isGrayModeEnabled()实现

    return true;
}

bool VideoEngine::get_current_gpio_state() {
    int fd = open(DEVICE_PATH, O_RDWR);
    if (fd < 0) {
        log_handler_->error("Failed to open dark mode device: {}", DEVICE_PATH);
        return false;
    }

    uint8_t mode_result = 0;
    int ioctl_ret = ioctl(fd, DARK_GET_MODE, &mode_result);
    close(fd);

    if (ioctl_ret < 0) {
        log_handler_->error("Failed to get current GPIO state via ioctl");
        return false;
    }

    if (mode_result == 0) {
        log_handler_->debug("Current GPIO state: LOW (COLOR mode)");
    } else if (mode_result == 1) {
        log_handler_->debug("Current GPIO state: HIGH (GRAY mode)");
    } else {
        log_handler_->error("Unknown GPIO state returned: {}", mode_result);
        return false;
    }

    return true;
}

bool VideoEngine::verify_camera_mode_switch(uint8_t target_mode) {
    // 这个方法用于验证相机是否成功切换到目标模式
    // 由于相机的实际切换是通过gray_mode_enabled_标志在video_detector_engine.cc中实现的
    // 我们这里主要是确保标志已经正确设置

    bool expected_gray_enabled = (target_mode == 1);
    bool actual_gray_enabled = gray_mode_enabled_.load();

    if (expected_gray_enabled == actual_gray_enabled) {
        log_handler_->debug("Camera mode switch verification passed: target_mode={}, gray_enabled={}",
                           target_mode, actual_gray_enabled);
        return true;
    } else {
        log_handler_->error("Camera mode switch verification failed: target_mode={}, gray_enabled={}",
                           target_mode, actual_gray_enabled);
        return false;
    }
}

uint16_t VideoEngine::calculate_median(const std::vector<uint16_t>& data) {
    if (data.empty()) {
        return 0;
    }

    std::vector<uint16_t> sorted_data = data;
    std::sort(sorted_data.begin(), sorted_data.end());

    size_t size = sorted_data.size();
    if (size % 2 == 0) {
        // 偶数个元素，取中间两个的平均值
        return (sorted_data[size/2 - 1] + sorted_data[size/2]) / 2;
    } else {
        // 奇数个元素，取中间的元素
        return sorted_data[size/2];
    }
}

bool VideoEngine::has_jump_variation(const std::deque<LightDataPair>& window) {
    if (window.size() < 2) {
        return false;
    }

    // 提取ch0通道数据
    std::vector<uint16_t> ch0_data;
    for (const auto& pair : window) {
        ch0_data.push_back(pair.ch0);
    }

    // 找到最大值和最小值
    auto minmax = std::minmax_element(ch0_data.begin(), ch0_data.end());
    uint16_t min_val = *minmax.first;
    uint16_t max_val = *minmax.second;

    // 判断是否有跳变（差值大于100）
    return (max_val - min_val) > JUMP_THRESHOLD;
}

bool VideoEngine::should_switch_mode(const std::deque<LightDataPair>& window) {
    if (window.size() != WINDOW_SIZE) {
        log_handler_->debug("Window size insufficient: {} < {}", window.size(), WINDOW_SIZE);
        return false;
    }

    // 提取ch0通道数据
    std::vector<uint16_t> ch0_data;
    for (const auto& pair : window) {
        ch0_data.push_back(pair.ch0);
    }

    bool has_jump = has_jump_variation(window);
    uint8_t current_mode = current_video_mode_.load();

    // log_handler_->debug("Mode switch analysis: current_mode={}, has_jump={}", current_mode, has_jump);

    if (!has_jump) {
        // 没有跳变，使用中位数进行判断
        uint16_t median_ch0 = calculate_median(ch0_data);

        // log_handler_->debug("No jump detected, using median: median_ch0={}", median_ch0);

        if (current_mode == 0) { // COLOR mode
            // 当前是RGB模式，检查是否需要切换到灰度模式
            // log_handler_->debug("COLOR mode check: median_ch0={} vs threshold={}",
            //                    median_ch0, light_thresholds_.rgb_to_gray.ch0);
            if (median_ch0 < light_thresholds_.rgb_to_gray.ch0) {
                log_handler_->info("Switching from COLOR to GRAY mode: median_ch0={} < threshold={}",
                                 median_ch0, light_thresholds_.rgb_to_gray.ch0);
                return true;
            }
        } else if (current_mode == 1) { // GRAY mode
            // 当前是灰度模式，检查是否需要切换到RGB模式
            // log_handler_->debug("GRAY mode check: median_ch0={} vs threshold={}",
            //                    median_ch0, light_thresholds_.gray_to_rgb.ch0);
            if (median_ch0 > light_thresholds_.gray_to_rgb.ch0) {
                log_handler_->info("Switching from GRAY to COLOR mode: median_ch0={} > threshold={}",
                                 median_ch0, light_thresholds_.gray_to_rgb.ch0);
                return true;
            }
        }
    } else {
        // 有跳变，使用计数方式进行判断
        if (current_mode == 1) { // GRAY mode
            // 当前是灰度模式，统计大于阈值的数据个数
            size_t count_above_threshold = 0;
            for (uint16_t value : ch0_data) {
                if (value > light_thresholds_.gray_to_rgb.ch0) {
                    count_above_threshold++;
                }
            }

            if (count_above_threshold >= COUNT_THRESHOLD) {
                log_handler_->info("Switching from GRAY to COLOR mode: {}/{} samples above threshold={}",
                                 count_above_threshold, ch0_data.size(), light_thresholds_.gray_to_rgb.ch0);
                return true;
            }
        } else if (current_mode == 0) { // COLOR mode
            // 当前是RGB模式，统计小于阈值的数据个数
            size_t count_below_threshold = 0;
            for (uint16_t value : ch0_data) {
                if (value < light_thresholds_.rgb_to_gray.ch0) {
                    count_below_threshold++;
                }
            }

            if (count_below_threshold >= COUNT_THRESHOLD) {
                log_handler_->info("Switching from COLOR to GRAY mode: {}/{} samples below threshold={}",
                                 count_below_threshold, ch0_data.size(), light_thresholds_.rgb_to_gray.ch0);
                return true;
            }
        }
    }

    return false;
}

bool VideoEngine::set_video_mode(uint8_t mode) {
    std::lock_guard<std::mutex> lock(video_mode_mutex_);  // 防止并发切换

    // log_handler_->info("Attempting to switch video mode to: {}", mode == 1 ? "GRAY" : "COLOR");

    // 检查是否已经是目标模式
    uint8_t current_mode = current_video_mode_.load();
    if (current_mode == mode) {
        log_handler_->debug("Already in target mode {}, skipping switch", mode == 1 ? "GRAY" : "COLOR");
        return true;
    }

    // 检查冷却时间，防止频繁切换
    auto now = std::chrono::steady_clock::now();
    if (now - last_mode_switch_time_ < MODE_SWITCH_COOLDOWN) {
        auto remaining = std::chrono::duration_cast<std::chrono::seconds>(
            MODE_SWITCH_COOLDOWN - (now - last_mode_switch_time_)).count();
        log_handler_->debug("Mode switch cooldown active, remaining: {}s", remaining);
        return false;
    }

    // 1. 首先切换相机模式（通过设置gray_mode_enabled_标志）
    bool target_gray_enabled = (mode == 1);
    uint8_t old_mode = current_mode;

    // 先更新相机模式标志，让相机开始按新模式工作
    gray_mode_enabled_.store(target_gray_enabled);
    current_video_mode_.store(mode);

    log_handler_->debug("Camera mode flag updated: gray_enabled={}", target_gray_enabled);

    // 2. 给相机一些时间来切换模式（可选，根据实际需要调整）
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 3. 验证相机模式切换是否成功
    if (!verify_camera_mode_switch(mode)) {
        log_handler_->error("Camera mode switch verification failed, rolling back");
        // 回滚到原来的状态
        gray_mode_enabled_.store(old_mode == 1);
        current_video_mode_.store(old_mode);
        return false;
    }

    // 4. 相机切换成功后，更新GPIO状态
    int fd = open(DEVICE_PATH, O_RDWR);
    if (fd < 0) {
        log_handler_->error("Failed to open dark mode device: {}", DEVICE_PATH);
        // GPIO更新失败，但相机已经切换，记录警告但不回滚
        log_handler_->warn("Camera switched to {} mode but GPIO update failed",
                          mode == 1 ? "GRAY" : "COLOR");
        return true; // 相机切换成功，即使GPIO更新失败也返回true
    }

    int ioctl_ret = 0;
    bool gpio_success = false;

    if (mode == 1) { // GRAY mode
        uint8_t result = 1;  // 传递期望设置的值（高电平）
        ioctl_ret = ioctl(fd, DARK_SET_HIGH, &result);
        if (ioctl_ret == 0) {
            if (result == 1) {
                log_handler_->info("Successfully updated GPIO to HIGH for GRAY mode, actual state: {}", result);
                gpio_success = true;
            } else {
                log_handler_->error("GPIO set HIGH command succeeded but actual state is: {}", result);
            }
        } else {
            log_handler_->error("Failed to set GPIO HIGH for GRAY mode, ioctl returned: {}", ioctl_ret);
        }
    } else if (mode == 0) { // COLOR mode
        uint8_t result = 0;  // 传递期望设置的值（低电平）
        ioctl_ret = ioctl(fd, DARK_SET_LOW, &result);
        if (ioctl_ret == 0) {
            if (result == 0) {
                log_handler_->info("Successfully updated GPIO to LOW for COLOR mode, actual state: {}", result);
                gpio_success = true;
            } else {
                log_handler_->error("GPIO set LOW command succeeded but actual state is: {}", result);
            }
        } else {
            log_handler_->error("Failed to set GPIO LOW for COLOR mode, ioctl returned: {}", ioctl_ret);
        }
    } else {
        log_handler_->error("Invalid video mode: {}", mode);
        close(fd);
        return false;
    }

    close(fd);

    // 更新最后切换时间
    last_mode_switch_time_ = std::chrono::steady_clock::now();

    if (gpio_success) {
        log_handler_->info("Video mode successfully switched to: {} (Camera and GPIO synchronized)",
                          mode == 1 ? "GRAY" : "COLOR");
    } else {
        log_handler_->warn("Video mode switched to: {} (Camera switched but GPIO sync failed)",
                          mode == 1 ? "GRAY" : "COLOR");
    }

    return true; // 只要相机切换成功就返回true
}

} // namespace aby_box