// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "module/box_detector.hpp"
#include "utils/utils.hpp"
#include <cmath>
#include <ctime>
#include <curl/curl.h>
namespace aby_box {

BoxDetector::BoxDetector(const std::string &module_name)
    : BaseModule(module_name) {
  auto &config = ConfigManager::getInstance();
  WEIGHT_API_URL = config.getBaseUrl() + ":8080/upload/weight";
  AUTH_TOKEN = config.getPermanentAccessToken();
}

BoxDetector::~BoxDetector() {}

bool BoxDetector::init() {
  reset_cusum();
  last_accel_y_ = 0.0f;
  last_event_time_ = 0;
  cat_in_box_ = false;
  weight_stable_ = true;
  last_weight_change_time_ = 0;

  // 初始化高斯滤波器
  init_gaussian_kernel();
  weight_buffer_.fill(0.0f);
  buffer_index_ = 0;

  // 初始化CURL
  curl_ = curl_easy_init();
  if (!curl_) {
    log_handler_->error("Failed to initialize CURL");
    return false;
  }

  // 设置CURL选项
  curl_easy_setopt(curl_, CURLOPT_URL, WEIGHT_API_URL.c_str());
  curl_easy_setopt(curl_, CURLOPT_WRITEFUNCTION, write_callback);

  // 设置HTTP头
  headers_ = nullptr;
  headers_ = curl_slist_append(headers_, "Content-Type: application/json");
  std::string auth_header = "X-API-Token: " + AUTH_TOKEN;
  headers_ = curl_slist_append(headers_, auth_header.c_str());
  curl_easy_setopt(curl_, CURLOPT_HTTPHEADER, headers_);

  return true;
}

bool BoxDetector::start() {
  is_running_ = true;
  sensor_thread_ =
      std::make_unique<std::thread>(&BoxDetector::sensor_subscriber, this);
  // 设置线程名称
  pthread_setname_np(sensor_thread_->native_handle(), "aby_box_sensor");
  
  detection_thread_ =
      std::make_unique<std::thread>(&BoxDetector::detection_loop, this);
  // 设置线程名称
  pthread_setname_np(detection_thread_->native_handle(), "aby_box_detect");
  return true;
}

bool BoxDetector::stop() {
  is_running_ = false;
  return true;
}

void BoxDetector::join() {
  if (detection_thread_ && detection_thread_->joinable()) {
    detection_thread_->join();
  }
  if (sensor_thread_ && sensor_thread_->joinable()) {
    sensor_thread_->join();
  }

  if (headers_) {
    curl_slist_free_all(headers_);
    headers_ = nullptr;
  }

  if (curl_) {
    curl_easy_cleanup(curl_);
    curl_ = nullptr;
  }
}

void BoxDetector::init_gaussian_kernel() {
  float sum = 0.0f;
  const float center = (GAUSSIAN_WINDOW - 1) / 2.0f;

  // 计算高斯核
  for (size_t i = 0; i < GAUSSIAN_WINDOW; ++i) {
    float x = static_cast<float>(i) - center;
    gaussian_kernel_[i] =
        std::exp(-(x * x) / (2.0f * GAUSSIAN_SIGMA * GAUSSIAN_SIGMA));
    sum += gaussian_kernel_[i];
  }

  // 归一化
  for (size_t i = 0; i < GAUSSIAN_WINDOW; ++i) {
    gaussian_kernel_[i] /= sum;
  }
}

float BoxDetector::apply_gaussian_filter(float new_weight) {
  // 更新缓冲区
  weight_buffer_[buffer_index_] = new_weight;
  buffer_index_ = (buffer_index_ + 1) % GAUSSIAN_WINDOW;

  // 应用高斯滤波
  float filtered_weight = 0.0f;
  for (size_t i = 0; i < GAUSSIAN_WINDOW; ++i) {
    size_t idx = (buffer_index_ + i) % GAUSSIAN_WINDOW;
    filtered_weight += weight_buffer_[idx] * gaussian_kernel_[i];
  }

  return filtered_weight;
}

void BoxDetector::sensor_subscriber() {
  uorb::SubscriptionData<uorb::msg::sensor_accel> sub_sensor_accel;
  uorb::SubscriptionData<uorb::msg::sensor_weight> sub_sensor_weight;
  uorb::SubscriptionData<uorb::msg::weight_base> sub_weight_base;
  uorb::SubscriptionData<uorb::msg::camera_object_detection> sub_camera;
  int timeout_ms = 100;

  struct orb_pollfd poll_fds[] = {
      {.fd = sub_sensor_accel.handle(), .events = POLLIN, .revents = 0},
      {.fd = sub_sensor_weight.handle(), .events = POLLIN, .revents = 0},
      {.fd = sub_weight_base.handle(), .events = POLLIN, .revents = 0},
      {.fd = sub_camera.handle(), .events = POLLIN, .revents = 0}};

  while (is_running_) {
    if (0 < orb_poll(poll_fds, ARRAY_SIZE(poll_fds), timeout_ms)) {
      // 更新加速度数据
      if (poll_fds[0].revents & POLLIN && sub_sensor_accel.Update()) {
        auto data = sub_sensor_accel.get();
        latest_accel_x_ = data.x;
        latest_accel_y_ = data.y;
        latest_accel_z_ = data.z;
      }

      // 更新重量数据
      if (poll_fds[1].revents & POLLIN && sub_sensor_weight.Update()) {
        auto data = sub_sensor_weight.get();
        if (data.weight < config_.weight_limitation) {
          // 应用高斯滤波
          // float filtered_weight = apply_gaussian_filter(data.weight);
          float filtered_weight = data.weight;
          latest_weight_ = filtered_weight;

          // 发送重量数据到服务器
          // send_weight_data(filtered_weight);
        }
      }

      // 处理基础重量数据
      if (poll_fds[2].revents & POLLIN && sub_weight_base.Update()) {
        auto data = sub_weight_base.get();
        config_.base_weight = data.base_weight;
        // log_handler_->debug("Updated base weight: {:.2f}g",
        // config_.base_weight);
      }

      // 处理图像检测结果
      if (poll_fds[3].revents & POLLIN && sub_camera.Update()) {
        auto data = sub_camera.get();
        if (data.object_type == OBJECT_CAT && data.confidence > 0.7f) {
          cat_detected_by_camera_ = true;
          last_cat_detection_time_ = data.timestamp;
        }
      }
    }
  }
}

bool BoxDetector::detect_weight_change(float weight) {
  // 如果重量低于基础重量，不触发变化检测
  if (is_weight_below_base(weight)) {
    return false;
  }

  float diff = weight - prev_weight_;

  // 更新CUSUM值
  cusum_pos_ = std::max(0.0f, cusum_pos_ + (diff - config_.drift));
  cusum_neg_ = std::min(0.0f, cusum_neg_ + (diff + config_.drift));

  // 记录重量变化时间
  if (std::abs(diff) > config_.weight_threshold * 0.1f) {
    last_weight_change_time_ = orb_absolute_time_us();
    weight_stable_ = false;
  } else if (orb_absolute_time_us() - last_weight_change_time_ >
             WEIGHT_STABLE_TIME_US) {
    weight_stable_ = true;
  }

  prev_weight_ = weight;
  return weight_stable_ && (cusum_pos_ > config_.cusum_threshold ||
                            cusum_neg_ < -config_.cusum_threshold);
}

void BoxDetector::reset_cusum() {
  cusum_pos_ = 0.0f;
  cusum_neg_ = 0.0f;
  prev_weight_ = latest_weight_.load();
}

bool BoxDetector::verify_cat_presence(uint64_t event_time) {
  uint64_t start_time = event_time;
  uint64_t current_time;

  // 等待图像验证，最多等待5秒
  do {
    current_time = orb_absolute_time_us();
    if (cat_detected_by_camera_ && last_cat_detection_time_ > event_time) {
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
  } while (current_time - start_time < IMAGE_VERIFY_TIMEOUT_US);

  return false;
}

void BoxDetector::check_cat_event() {
  uint64_t current_time = orb_absolute_time_us();

  // 检查冷却时间
  if (current_time - last_event_time_.load() < EVENT_COOLDOWN_US) {
    return;
  }

  float weight = latest_weight_.load();
  float accel_y = latest_accel_y_.load();
  float last_y = last_accel_y_.load();
  float accel_diff = std::abs(accel_y - last_y);

  last_accel_y_.store(accel_y);

  // 如果重量低于基础重量，可能是猫砂减少，不触发任何事件
  if (is_weight_below_base(weight)) {
    return;
  }

  if (accel_diff > config_.accel_threshold || detect_weight_change(weight)) {
    bool is_enter_event = (cusum_pos_ > config_.cusum_threshold);
    bool is_leave_event = (cusum_neg_ < -config_.cusum_threshold) &&
                          is_weight_within_base(weight) || is_weight_within_base(weight);

    // 正常重量范围内的事件处理
    if (is_enter_event && !cat_in_box_ && is_weight_above_base(weight)) {
      if (verify_cat_presence(current_time)) {
        uint64_t timestamp = get_current_timestamp_second();
        APIClient::getInstance().startRecording(timestamp);
        publish_cat_event(CAT_ENTER, timestamp);
        cat_in_box_ = true;
        last_event_time_.store(current_time);
        reset_cusum();
      }
    } else if (is_leave_event && cat_in_box_ && !is_weight_below_base(weight)) {
      uint64_t event_time = orb_absolute_time_us();
      if (!verify_cat_presence(event_time)) {
        uint64_t timestamp = get_current_timestamp_second();
        APIClient::getInstance().stopRecording();
        publish_cat_event(CAT_LEAVE, timestamp);
        cat_in_box_ = false;
        last_event_time_.store(event_time);
        reset_cusum();
      }
    }
  }
}

void BoxDetector::publish_cat_event(uint8_t event_type, int64_t timestamp) {
  auto &event = pub_cat_event_.get();
  auto now = std::chrono::system_clock::now();
  event.timestamp = timestamp;

  event.device_id = 0;
  event.event_type = event_type;

  // 获取原子变量的值
  float weight = latest_weight_.load();
  float accel_x = latest_accel_x_.load();
  float accel_y = latest_accel_y_.load();
  float accel_z = latest_accel_z_.load();

  event.weight = weight;
  event.accel_x = accel_x;
  event.accel_y = accel_y;
  event.accel_z = accel_z;

  if (!pub_cat_event_.Publish()) {
    log_handler_->error("Failed to publish cat event");
  }

  float accel_magnitude =
      std::sqrt(accel_x * accel_x + accel_y * accel_y + accel_z * accel_z);

  log_handler_->info("Cat {} detected. Weight: {:.2f}g, Accel: {:.2f}m/s²",
                     event_type == CAT_ENTER ? "entered" : "left", weight,
                     accel_magnitude);
}

void BoxDetector::detection_loop() {
  while (is_running_) {
    check_cat_event();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
  }
}

void BoxDetector::send_weight_data(float weight) {
  if (!curl_) {
    return;
  }

  // 构造JSON数据
  std::string json_data = "{\"weight\": " + std::to_string(weight) + "}";

  // 设置POST数据
  curl_easy_setopt(curl_, CURLOPT_POSTFIELDS, json_data.c_str());

  // 发送请求
  CURLcode res = curl_easy_perform(curl_);
  if (res != CURLE_OK) {
    log_handler_->error("Failed to send weight data: {}",
                        curl_easy_strerror(res));
  } else {
    long response_code;
    curl_easy_getinfo(curl_, CURLINFO_RESPONSE_CODE, &response_code);
    if (response_code != 200) {
      log_handler_->error("Server returned error code: {}", response_code);
    }
  }
}

} // namespace aby_box