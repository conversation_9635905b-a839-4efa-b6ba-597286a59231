// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "test/test_module.hpp"

namespace aby_box {

TestModule::TestModule(const std::string &module_name)
    : BaseModule(module_name), gpio_pin_(508), // 与原WiFi模块相同的引脚
      last_event_time_(0), cat_in_box_(false), is_running_(false) {}

TestModule::~TestModule() {
    stop();
}

bool TestModule::init() {
    log_handler_->info("Test module initialized, GPIO pin: {}", gpio_pin_);
    log_handler_->info("Test module ready for complete cat simulation testing");
    log_handler_->info("GPIO {} HIGH = CAT_ENTER + startRecording", gpio_pin_);
    log_handler_->info("GPIO {} LOW  = CAT_LEAVE + stopRecording", gpio_pin_);
    return true;
}

bool TestModule::start() {
    is_running_ = true;
    log_handler_->info("Test module started - monitoring GPIO {} for triggers", gpio_pin_);
    return true;
}

bool TestModule::stop() {
    is_running_ = false;
    
    // 取消GPIO回调注册
    if (gpio_manager_) {
        gpio_manager_->unregister_callback(gpio_pin_);
        gpio_manager_->remove_gpio(gpio_pin_);
        log_handler_->info("GPIO {} unregistered and removed", gpio_pin_);
    }
    
    log_handler_->info("Test module stopped");
    return true;
}

void TestModule::join() {
    // 测试模块没有额外的线程，所以join()方法为空
}

void TestModule::set_gpio_manager(std::shared_ptr<GpioManager> gpio_manager) {
    gpio_manager_ = gpio_manager;
    
    if (gpio_manager_) {
        // 配置GPIO引脚 - 监听上升沿和下降沿
        GpioConfig config(gpio_pin_, GpioDirection::INPUT, 1000);  // 增加到1000ms防抖
        config.edge = GpioEdge::BOTH;  // 监听上升沿和下降沿
        
        if (gpio_manager_->configure_gpio(config)) {
            // 注册回调函数
            auto callback = [this](int pin, int value, uint64_t timestamp) {
                this->on_gpio_event(pin, value, timestamp);
            };
            
            if (gpio_manager_->register_callback(gpio_pin_, callback)) {
                log_handler_->info("GPIO {} configured and callback registered successfully", gpio_pin_);
            } else {
                log_handler_->error("Failed to register GPIO {} callback", gpio_pin_);
            }
        } else {
            log_handler_->error("Failed to configure GPIO {}", gpio_pin_);
        }
    }
}

void TestModule::on_gpio_event(int pin, int value, uint64_t timestamp) {
    if (!is_running_) {
        return;
    }
    
    // GPIO管理器传递的是微秒时间戳，我们统一使用微秒进行处理
    uint64_t current_time_us = aby_box::get_current_timestamp_microsecond();
    uint64_t event_timestamp_second = current_time_us / 1000000;  // 转换为秒用于日志

    log_handler_->info("GPIO {} triggered with value {} at timestamp {}", pin, value, event_timestamp_second);
    
    // 防抖检查 - 使用微秒时间戳进行比较
    uint64_t time_diff_us = current_time_us - last_event_time_;
    if (time_diff_us < MIN_EVENT_INTERVAL_US) {
        log_handler_->debug("Event filtered - too close to previous event ({}ms ago)", 
                           time_diff_us / 1000);  // 显示毫秒数更直观
        return;
    }
    
    // 状态变化检查 - 避免重复处理相同状态
    static int last_gpio_value = -1;
    if (last_gpio_value == value) {
        log_handler_->debug("GPIO value unchanged ({}), ignoring duplicate signal", value);
        return;
    }
    last_gpio_value = value;
    
    last_event_time_ = current_time_us;
    
    // 根据GPIO状态处理猫的进入和离开
    if (value == 1) {
        // 高电平 - 猫进入
        if (!cat_in_box_) {
          handle_cat_enter(aby_box::get_current_timestamp_second());
          cat_in_box_ = true;
        } else {
            log_handler_->debug("Cat already in box, ignoring enter signal");
        }
    } else {
        // 低电平 - 猫离开
        if (cat_in_box_) {
          handle_cat_leave(aby_box::get_current_timestamp_second());
          cat_in_box_ = false;
        } else {
            log_handler_->debug("Cat already out of box, ignoring leave signal");
        }
    }
}

void TestModule::handle_cat_enter(uint64_t timestamp) {
    log_handler_->info("=== CAT ENTER SIMULATION ===");
    
    // 1. 启动录制
    log_handler_->info("Starting recording...");
    APIClient::getInstance().startRecording(timestamp);
    
    // 2. 发布CAT_ENTER事件
    log_handler_->info("Publishing CAT_ENTER event...");
    publish_cat_enter_event(timestamp);
    
    log_handler_->info("Cat enter simulation completed");
}

void TestModule::handle_cat_leave(uint64_t timestamp) {
    log_handler_->info("=== CAT LEAVE SIMULATION ===");
    
    // 1. 停止录制
    log_handler_->info("Stopping recording...");
    APIClient::getInstance().stopRecording();
    
    // 2. 发布CAT_LEAVE事件
    log_handler_->info("Publishing CAT_LEAVE event...");
    publish_cat_leave_event(timestamp);
    
    log_handler_->info("Cat leave simulation completed");
}

void TestModule::publish_cat_enter_event(uint64_t timestamp) {
    // 创建cat_event数据
    auto &data = pub_cat_event_.get();
    data.timestamp = timestamp;
    data.device_id = 1;  // 测试设备ID
    data.event_type = CAT_ENTER;
    data.weight = 0.0f;      // 测试时重量为0
    data.accel_x = 0.0f;     // 测试时加速度为0
    data.accel_y = 0.0f;
    data.accel_z = 0.0f;
    
    // 发布事件
    if (pub_cat_event_.Publish()) {
        log_handler_->info("CAT_ENTER event published successfully");
        log_handler_->info("Image capture should start now - check ImageCaptureManager logs");
    } else {
        log_handler_->error("Failed to publish CAT_ENTER event");
    }
}

void TestModule::publish_cat_leave_event(uint64_t timestamp) {
    // 创建cat_event数据
    auto &data = pub_cat_event_.get();
    data.timestamp = timestamp;
    data.device_id = 1;  // 测试设备ID
    data.event_type = CAT_LEAVE;
    data.weight = 0.0f;      // 测试时重量为0
    data.accel_x = 0.0f;     // 测试时加速度为0
    data.accel_y = 0.0f;
    data.accel_z = 0.0f;
    
    // 发布事件
    if (pub_cat_event_.Publish()) {
        log_handler_->info("CAT_LEAVE event published successfully");
        log_handler_->info("Image capture should stop now - check ImageCaptureManager logs");
    } else {
        log_handler_->error("Failed to publish CAT_LEAVE event");
    }
}

} // namespace aby_box 