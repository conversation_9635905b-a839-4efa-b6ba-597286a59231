// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "sensor/lighting.hpp"
#include "utils/api_client.h"
#include "common/topics/sensor_light.hpp"
#include "uorb/publication.h"
#include "uorb/abs_time.h"
#include <filesystem>
#include <thread>

namespace aby_box {

// 静态标志位，用于跟踪light传感器错误是否已被清除
static std::atomic<bool> light_sensor_error_cleared{false};
// 静态标志位，用于跟踪light传感器错误是否已被报告
static std::atomic<bool> light_sensor_error_reported{false};

// 安全的错误报告函数（只执行一次）
static void reportLightSensorErrorOnce(const std::string& message) {
    bool expected = false;
    if (light_sensor_error_reported.compare_exchange_strong(expected, true)) {
        // 异步报告light传感器错误状态，避免阻塞主线程
        std::thread([message]() {
            try {
                // 构造错误码和错误消息（限制长度以符合数据库列限制）
                int error_code = 1; // 通用错误码
                std::string error_message = message;
                if (error_message.length() > 50) {
                    error_message = error_message.substr(0, 47) + "...";
                }

                std::string additional_info = "LTR303 device error";
                if (additional_info.length() > 30) {
                    additional_info = additional_info.substr(0, 27) + "...";
                }

                // 调用API上报传感器错误
                bool success = APIClient::getInstance().reportSensorError(
                    "light", error_code, error_message, additional_info);

                if (success) {
                    printf("Light sensor error successfully reported to server\n");
                } else {
                    printf("Failed to report light sensor error to server\n");
                    // 报告失败时重置标志位，允许下次重试
                    std::this_thread::sleep_for(std::chrono::seconds(30));
                    light_sensor_error_reported.store(false);
                }
            } catch (const std::exception& e) {
                printf("Exception while reporting light sensor error to server: %s\n", e.what());
                // 异常时重置标志位，允许下次重试
                std::this_thread::sleep_for(std::chrono::seconds(30));
                light_sensor_error_reported.store(false);
            } catch (...) {
                printf("Unknown exception while reporting light sensor error to server\n");
                // 异常时重置标志位，允许下次重试
                std::this_thread::sleep_for(std::chrono::seconds(30));
                light_sensor_error_reported.store(false);
            }
        }).detach();
    }
}

// 清除light传感器错误状态（只执行一次）
static void clearLightSensorErrorOnce() {
    bool expected = false;
    if (light_sensor_error_cleared.compare_exchange_strong(expected, true)) {
        // 异步清除light传感器错误状态，避免阻塞主线程
        std::thread([]() {
            try {
                bool success = APIClient::getInstance().clearSensorError("light");

                if (success) {
                    printf("Light sensor error status successfully cleared on server\n");
                    // 清除成功时，重置错误报告标志位，允许下次错误时重新报告
                    light_sensor_error_reported.store(false);
                } else {
                    printf("Failed to clear light sensor error status on server\n");
                    // 清除失败时重置标志位，允许下次重试
                    std::this_thread::sleep_for(std::chrono::seconds(30));
                    light_sensor_error_cleared.store(false);
                }
            } catch (const std::exception& e) {
                printf("Exception while clearing light sensor error status: %s\n", e.what());
                // 异常时重置标志位，允许下次重试
                std::this_thread::sleep_for(std::chrono::seconds(30));
                light_sensor_error_cleared.store(false);
            } catch (...) {
                printf("Unknown exception while clearing light sensor error status\n");
                // 异常时重置标志位，允许下次重试
                std::this_thread::sleep_for(std::chrono::seconds(30));
                light_sensor_error_cleared.store(false);
            }
        }).detach();
    }
}

Lighting::Lighting(const std::string &module_name)
    : BaseModule(module_name), is_running_(false) {}

Lighting::~Lighting() {
  if (is_running_) {
    stop();
  }
  join();
}

bool Lighting::init() {
  fd_ = open("/dev/ltr303", O_RDWR);
  if (fd_ < 0) {
    log_handler_->error("Failed to open LTR303 device");
    // 报告light传感器错误
    reportLightSensorErrorOnce("Failed to open LTR303 device");
    return false;
  } else {
    // 设备打开成功，清除错误状态
    clearLightSensorErrorOnce();
  }

  // 初始化传感器
  if (ioctl(fd_, LTR303_IOC_INIT_SENSOR) < 0) {
    log_handler_->error("Failed to initialize LTR303 sensor");
    // 报告light传感器错误
    reportLightSensorErrorOnce("Failed to initialize LTR303 sensor");
    close(fd_);
    fd_ = -1;
    return false;
  }

  return true;
}

bool Lighting::start() {
  is_running_ = true;
  thread_publisher_ = std::thread(&Lighting::thread_publisher, this);
  // 设置线程名称
  pthread_setname_np(thread_publisher_.native_handle(), "aby_lighting");
  return true;
}

bool Lighting::stop() {
  is_running_ = false;
  return true;
}

void Lighting::join() {
  if (is_running_) {
    stop();
  }
  if (thread_publisher_.joinable()) {
    thread_publisher_.join();
  }
  if (fd_ > 0) {
    close(fd_);
    fd_ = -1;
  }
}

void Lighting::thread_publisher() {
  uorb::PublicationData<uorb::msg::sensor_light> pub_sensor_light;
  struct ltr303_data data;
  
  while (!is_running_) {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }
  
  while (is_running_) {
    // 读取传感器数据
    if (ioctl(fd_, LTR303_IOC_READ_DATA, &data) < 0) {
      // log_handler_->error("Failed to read LTR303 data");
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      continue;
    }
    
    auto &msg = pub_sensor_light.get();
    msg.timestamp = orb_absolute_time_us();
    msg.device_id = 0;
    msg.light_visible = data.ch0;
    msg.light_ir = data.ch1;
    
    if (!pub_sensor_light.Publish()) {
      log_handler_->error("Failed to publish light data");
    }

    // log_handler_->info("Lighting: visible={}, ir={}", data.ch0, data.ch1);
    
    std::this_thread::sleep_for(std::chrono::milliseconds(200)); // 200ms采样间隔
  }
}

} // namespace aby_box