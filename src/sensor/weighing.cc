// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "sensor/weighing.hpp"
#include "utils/api_client.h"
#include <filesystem>
#include <json-c/json.h>
#include <thread>
#include <algorithm>
#include <numeric>

namespace aby_box {

// 静态标志位，用于跟踪weight_sensor错误是否已被清除
static std::atomic<bool> weight_sensor_error_cleared{false};
// 静态标志位，用于跟踪weight_sensor错误是否已被报告
static std::atomic<bool> weight_sensor_error_reported{false};

// 安全的错误报告函数（只执行一次）
static void reportWeightSensorErrorOnce(const std::string& message) {
    bool expected = false;
    if (weight_sensor_error_reported.compare_exchange_strong(expected, true)) {
        // 异步报告weight_sensor错误状态，避免阻塞主线程
        std::thread([message]() {
            try {
                // 构造错误码和错误消息（限制长度以符合数据库列限制）
                int error_code = 1; // 通用错误码
                std::string error_message = message;
                if (error_message.length() > 50) {
                    error_message = error_message.substr(0, 47) + "...";
                }

                std::string additional_info = "HX711 sensor error";
                if (additional_info.length() > 30) {
                    additional_info = additional_info.substr(0, 27) + "...";
                }

                // 调用API上报传感器错误
                bool success = APIClient::getInstance().reportSensorError(
                    "weight_sensor", error_code, error_message, additional_info);

                if (success) {
                    printf("Weight sensor error successfully reported to server\n");
                } else {
                    printf("Failed to report weight sensor error to server\n");
                    // 报告失败时重置标志位，允许下次重试
                    std::this_thread::sleep_for(std::chrono::seconds(30));
                    weight_sensor_error_reported.store(false);
                }
            } catch (const std::exception& e) {
                printf("Exception while reporting weight sensor error to server: %s\n", e.what());
                // 异常时重置标志位，允许下次重试
                std::this_thread::sleep_for(std::chrono::seconds(30));
                weight_sensor_error_reported.store(false);
            } catch (...) {
                printf("Unknown exception while reporting weight sensor error to server\n");
                // 异常时重置标志位，允许下次重试
                std::this_thread::sleep_for(std::chrono::seconds(30));
                weight_sensor_error_reported.store(false);
            }
        }).detach();
    }
}

// 清除weight_sensor错误状态（只执行一次）
static void clearWeightSensorErrorOnce() {
    bool expected = false;
    if (weight_sensor_error_cleared.compare_exchange_strong(expected, true)) {
        // 异步清除weight_sensor错误状态，避免阻塞主线程
        std::thread([]() {
            try {
                bool success = APIClient::getInstance().clearSensorError("weight_sensor");

                if (success) {
                    printf("Weight sensor error status successfully cleared on server\n");
                    // 清除成功时，重置错误报告标志位，允许下次错误时重新报告
                    weight_sensor_error_reported.store(false);
                } else {
                    printf("Failed to clear weight sensor error status on server\n");
                    // 清除失败时重置标志位，允许下次重试
                    std::this_thread::sleep_for(std::chrono::seconds(30));
                    weight_sensor_error_cleared.store(false);
                }
            } catch (const std::exception& e) {
                printf("Exception while clearing weight sensor error status: %s\n", e.what());
                // 异常时重置标志位，允许下次重试
                std::this_thread::sleep_for(std::chrono::seconds(30));
                weight_sensor_error_cleared.store(false);
            } catch (...) {
                printf("Unknown exception while clearing weight sensor error status\n");
                // 异常时重置标志位，允许下次重试
                std::this_thread::sleep_for(std::chrono::seconds(30));
                weight_sensor_error_cleared.store(false);
            }
        }).detach();
    }
}

Weighing::Weighing(const std::string &module_name, bool enable_debug, bool calibration_mode)
    : BaseModule(module_name), calibration_mode_(calibration_mode) {
    // Initialize weight filter
    weight_filter_ = std::make_unique<WeightFilter>();
    weight_filter_->windowSize = 5;
    weight_filter_->currentSize = 0;
    weight_filter_->spikeThreshold = 0.015; // 1.5%
    weight_filter_->stableThreshold = 0.001; // 0.1%
    weight_filter_->minStableCount = 3;
    weight_filter_->isFirstFrame = true;
    weight_filter_->lastValidValue = 0.0;
    weight_filter_->consecutiveChanges = 0;
    weight_filter_->lastChange = 0.0;
    weight_filter_->window.resize(weight_filter_->windowSize);
    
    // ... rest of existing initialization ...
}

Weighing::~Weighing() {
    if (is_running_) {
        stop();
    }
    join();
}

bool Weighing::init() {
    fd_ = open("/dev/hx711", O_RDWR);
    if (fd_ < 0) {
        log_handler_->error("Failed to open HX711 device");
        return false;
    }

    if (!calibration_mode_) {
        if (!load_calibration()) {
            log_handler_->error("Failed to load calibration data");
            return false;
        }
    }
    return true;
}

bool Weighing::start() {
    is_running_ = true;
    thread_publisher_ = std::thread(&Weighing::thread_publisher, this);
    // 设置线程名称
    pthread_setname_np(thread_publisher_.native_handle(), "aby_weighing");
    return true;
}

bool Weighing::stop() {
    is_running_ = false;
    return true;
}

void Weighing::join() {
    if (is_running_) {
        stop();
    }
    if (thread_publisher_.joinable()) {
        thread_publisher_.join();
    }
    if (fd_ > 0) {
        close(fd_);
        fd_ = -1;
    }
}

double Weighing::calculate_mean(const std::vector<double>& data) {
    double sum = 0.0;
    for (const auto& value : data) {
        sum += value;
    }
    return sum / data.size();
}

bool Weighing::is_data_stable(const WeightFilter* filter, double mean) {
    size_t stableCount = 0;
    for (size_t i = 0; i < filter->currentSize; i++) {
        if (std::abs(filter->window[i] - mean) / mean <= filter->stableThreshold) {
            stableCount++;
        }
    }
    return stableCount >= filter->minStableCount;
}

void Weighing::add_to_window(WeightFilter* filter, double value) {
    if (filter->currentSize < filter->windowSize) {
        filter->window[filter->currentSize++] = value;
    } else {
        // Move window
        std::rotate(filter->window.begin(), filter->window.begin() + 1, filter->window.end());
        filter->window[filter->windowSize - 1] = value;
    }
}

double Weighing::filter_weight_signal(double raw_weight) {
    // Handle first frame
    if (weight_filter_->isFirstFrame) {
        weight_filter_->isFirstFrame = false;
        weight_filter_->lastValidValue = raw_weight;
        return raw_weight;
    }
    
    // Check for changes in specific ranges
    double weightDiff = raw_weight - weight_filter_->lastValidValue;
    if ((weightDiff > 10.0 && weightDiff < 100.0) ||    // Positive change range
        (weightDiff < -10.0 && weightDiff > -100.0)) {  // Negative change range
        // Keep last valid value for changes in these ranges
        // log_handler_->debug("Change in filter range: {:.2f}, keeping last value: {:.2f}", 
        //                   weightDiff, weight_filter_->lastValidValue);
        return weight_filter_->lastValidValue;
    }
    
    // Handle first valid value
    if (weight_filter_->currentSize == 0) {
        add_to_window(weight_filter_.get(), raw_weight);
        weight_filter_->lastValidValue = raw_weight;
        weight_filter_->consecutiveChanges = 0;
        weight_filter_->lastChange = 0.0;
        return raw_weight;
    }
    
    // Calculate current window mean
    double mean = calculate_mean(weight_filter_->window);
    
    // Calculate relative change
    double relativeChange = (raw_weight - mean) / mean;
    double absChange = std::abs(relativeChange);
    
    // Check for spike
    if (absChange > weight_filter_->spikeThreshold) {
        // Check for consistent trend
        if (weight_filter_->consecutiveChanges > 0) {
            if ((relativeChange > 0 && weight_filter_->lastChange > 0) ||
                (relativeChange < 0 && weight_filter_->lastChange < 0)) {
                weight_filter_->consecutiveChanges++;
                
                // Accept value after 3 consistent changes
                if (weight_filter_->consecutiveChanges >= 3) {
                    weight_filter_->lastValidValue = raw_weight;
                    add_to_window(weight_filter_.get(), raw_weight);
                    weight_filter_->lastChange = relativeChange;
                    return raw_weight;
                }
            } else {
                weight_filter_->consecutiveChanges = 1;
            }
        } else {
            weight_filter_->consecutiveChanges = 1;
        }
        
        weight_filter_->lastChange = relativeChange;
        // log_handler_->debug("Spike detected! Value: {}, Mean: {}, Change: {:.2f}%", 
        //                   raw_weight, mean, absChange * 100);
        return weight_filter_->lastValidValue;
    }
    
    // Normal value processing
    weight_filter_->consecutiveChanges = 0;
    weight_filter_->lastChange = relativeChange;
    weight_filter_->lastValidValue = raw_weight;
    add_to_window(weight_filter_.get(), raw_weight);
    return raw_weight;
}
void Weighing::thread_publisher() {
    uorb::PublicationData<uorb::msg::sensor_weight> pub_sensor_weight;
    
    while (!is_running_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    while (is_running_) {
        int32_t raw_value;
        if (ioctl(fd_, HX711_GET_READING, &raw_value) < 0) {
            log_handler_->error("Failed to read weight sensor value");
            // 报告weight_sensor传感器错误
            reportWeightSensorErrorOnce("Failed to read HX711 sensor data");
            std::this_thread::sleep_for(std::chrono::milliseconds(10000));
            continue;
        }

        // 检查raw_value来判断传感器状态
        if (raw_value == 0) {
            log_handler_->error("Weight sensor returned invalid value (0)");
            // 报告weight_sensor传感器错误
            reportWeightSensorErrorOnce("HX711 sensor returned invalid zero value");
            std::this_thread::sleep_for(std::chrono::milliseconds(10000));
            continue;
        } else {
            // raw_value不为0，说明weight_sensor工作正常，清除错误状态
            clearWeightSensorErrorOnce();
        }

        // Calculate raw weight
        float raw_weight = (raw_value - calib_.offset) / calib_.scale_factor;
        
        // Apply filter
        float filtered_weight = filter_weight_signal(raw_weight);
        
        auto &data = pub_sensor_weight.get();
        data.timestamp = orb_absolute_time_us();
        data.device_id = 0;
        data.weight = filtered_weight;
        
        if (!pub_sensor_weight.Publish()) {
            log_handler_->error("Failed to publish weight data");
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

bool Weighing::calibrate() {
  if (!calibration_mode_) {
    spdlog::error("Not in calibration mode");
    return false;
  }

      spdlog::info("Starting calibration...");

      // Set Gain
      if (ioctl(fd_, HX711_SET_GAIN, HX711_GAIN_128) < 0) {
        spdlog::error("Failed to set gain");
        return false;
      }

      // Get offset
      if (ioctl(fd_, HX711_CALI_OFFSET, &calib_.offset) < 0) {
        spdlog::error("Failed to calibrate offset");
        return false;
      }

      spdlog::info("offset: {}", calib_.offset);

      spdlog::info("Please ensure the scale is empty");
      std::this_thread::sleep_for(std::chrono::seconds(5));

      spdlog::info("Place a 5000g weight on the scale");
      std::this_thread::sleep_for(std::chrono::seconds(5));

      // Get scale factor
      int32_t factor_temp = 0;
      if (ioctl(fd_, HX711_CALI_FACTOR, &factor_temp) < 0) {
        spdlog::error("Failed to calibrate scale factor");
        return false;
      }
      calib_.scale_factor =
          static_cast<float>(factor_temp) / calib_.calibration_weight;
      spdlog::info("Calibration completed, the weight is {}", calib_.scale_factor);

      return save_calibration();
    }

bool Weighing::load_calibration() {
  try {
    json_object *root = json_object_from_file(calib_file_.c_str());
    if (!root) {
      return false;
    }

        json_object *j_offset, *j_scale_factor;
        if (json_object_object_get_ex(root, "offset", &j_offset)) {
          calib_.offset = json_object_get_int(j_offset);
        }
        if (json_object_object_get_ex(root, "scale_factor", &j_scale_factor)) {
          calib_.scale_factor = json_object_get_double(j_scale_factor);
        }

        json_object_put(root);
        return true;
      } catch (const std::exception &e) {
        spdlog::error("Failed to load calibration: {}", e.what());
        return false;
      }
    }

bool Weighing::save_calibration() {
  try {
    std::filesystem::create_directories(
        std::filesystem::path(calib_file_).parent_path());

        json_object *root = json_object_new_object();
        json_object_object_add(root, "offset", json_object_new_int(calib_.offset));
        json_object_object_add(root, "scale_factor",
                               json_object_new_double(calib_.scale_factor));

        int ret = json_object_to_file_ext(calib_file_.c_str(), root,
                                          JSON_C_TO_STRING_PRETTY);
        json_object_put(root);

    return ret == 0;
  } catch (const std::exception &e) {
    spdlog::error("Failed to save calibration: {}", e.what());
    return false;
  }
}

} // namespace aby_box
