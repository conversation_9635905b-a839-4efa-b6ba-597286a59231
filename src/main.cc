// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "common/module_names.hpp"
#include "module/box_detector.hpp"
#include "module/statistics.hpp"
#include "module/cloud_sync_manager.hpp"
#include "module/video_engine/video_engine.hpp"
#include "module/weight_analyzer.hpp"
#include "module/wifi.hpp"
#include "module/ota/ota_manager.hpp"  // Added OTA Manager include
#include "module/image_capture_manager.hpp"  // Added Image Capture Manager include
#include "module/gpio_manager.hpp"  // Added GPIO Manager include
#include "module/bluetooth_manager.hpp"  // Added Bluetooth Manager include
#include "module/initialization_manager.hpp"  // Added Initialization Manager include
#include "test/test_module.hpp"  // Added Test Module include
#include "sensor/weighing.hpp"
#include "sensor/lighting.hpp"
#include "utils/api_client.h"
#include "utils/config_manager.hpp"
#include <spdlog/spdlog.h>
#include <signal.h>
#include <cvi_comm.h>

volatile bool bExit = false;
static void SampleHandleSig(CVI_S32 signo) {
  signal(SIGINT, SIG_IGN);
  signal(SIGTERM, SIG_IGN);
  printf("handle signal, signo: %d\n", signo);
  if (SIGINT == signo || SIGTERM == signo) {
    bExit = true;
  }
}

int main(int argc, char *argv[]) {
  signal(SIGINT, SampleHandleSig);
  signal(SIGTERM, SampleHandleSig);

  // 初始化配置管理器
  auto &config_manager = aby_box::ConfigManager::getInstance();
  if (!config_manager.init()) {
    spdlog::error("Failed to initialize config manager");
    return -1;
  }
  spdlog::info("Config manager initialized successfully");

  bool calibration_mode = false;
  bool test_mode = true;
  // 解析命令行参数
  for (int i = 1; i < argc; i++) {
    if (std::string(argv[i]) == "--calibrate") {
      calibration_mode = true;
      break;
    } else if (std::string(argv[i]) == "--test") {
      test_mode = true;
      break;
    }
  }
  if (calibration_mode) {
    spdlog::info("Running in calibration mode");

    auto weighing =
        std::make_shared<aby_box::Weighing>(aby_box::WEIGHING, true, true);

    // 初始化称重模块
    if (!weighing->init()) {
      spdlog::error("Failed to initialize weighing");
    }

    // 执行校准
    if (!weighing->calibrate()) {
      spdlog::error("Failed to calibrate weighing");
    }
    weighing->stop();
    weighing->join();
    spdlog::info("Weighing calibration successful");
    return 0;
  }

  // 正常运行模式
  spdlog::info("start aby_box");
  spdlog::info("use below command to check log:");
  spdlog::info("tail -n 200 /var/aby_box_log/aby_box.txt -f");

  auto box_detector =
      std::make_shared<aby_box::BoxDetector>(aby_box::BOX_DETECTOR);
  auto video_engine =
      std::make_shared<aby_box::VideoEngine>(aby_box::VIDEO_ENGINE);
  auto weight_analyzer =
      std::make_shared<aby_box::WeightAnalyzer>(aby_box::WEIGHT_ANALYZER);
  auto& wifi = aby_box::Wifi::getInstance();  // 使用单例模式
  auto weighing = std::make_shared<aby_box::Weighing>(aby_box::WEIGHING);
  auto lighting = std::make_shared<aby_box::Lighting>(aby_box::LIGHTING);
  auto stats = std::make_shared<aby_box::Statistics>(aby_box::STATISTICS);
  auto cloud_sync_manager = 
      std::make_shared<aby_box::CloudSyncManager>(aby_box::CLOUD_SYNC_MANAGER);
  auto ota_manager = std::make_shared<aby_box::OtaManager>(aby_box::OTA_MANAGER);
  auto image_capture_manager = std::make_shared<aby_box::ImageCaptureManager>("ImageCaptureManager");
  auto gpio_manager = std::make_shared<aby_box::GpioManager>(aby_box::GPIO_MANAGER);
  auto bluetooth_manager = std::make_shared<aby_box::BluetoothManager>(aby_box::BLUETOOTH_MANAGER);
  auto& initialization_manager = aby_box::InitializationManager::getInstance();
  
  // 根据参数决定是否创建测试模块
  std::shared_ptr<aby_box::TestModule> test_module = nullptr;

  // 按顺序初始化各个模块
  gpio_manager->init();
  bluetooth_manager->init();
  initialization_manager.init();
  wifi.init();  // 单例使用点操作符
  box_detector->init();
  weight_analyzer->init();
  weighing->init();
  video_engine->init();
  stats->init();
  cloud_sync_manager->init();
  image_capture_manager->init();
  lighting->init();

  // Register modules that should be stopped during OTA upgrades
  ota_manager->registerModule(aby_box::BOX_DETECTOR, box_detector);
  ota_manager->registerModule(aby_box::WEIGHT_ANALYZER, weight_analyzer);
  ota_manager->registerModule(aby_box::WEIGHING, weighing);

  cloud_sync_manager->setOtaManager(ota_manager);
  
  // 建立双向引用关系，让OtaManager能够通知CloudSyncManager失败信息
  ota_manager->setCloudSyncManager(cloud_sync_manager);
  
  // Connect APIClient with OtaManager for status checking
  aby_box::APIClient::getInstance().setOtaManager(ota_manager);

  ota_manager->init();
  aby_box::APIClient::getInstance().init();

  std::this_thread::sleep_for(std::chrono::milliseconds(100));

  gpio_manager->start();
  initialization_manager.start();
  wifi.start();  // 单例使用点操作符
  weighing->start();
  video_engine->start();
  box_detector->start();
  weight_analyzer->start();
  stats->start();
  cloud_sync_manager->start();
  image_capture_manager->start();
  ota_manager->start();
  lighting->start();
  bluetooth_manager->start();

  if (test_mode) {
    test_module = std::make_shared<aby_box::TestModule>(aby_box::TEST_MODULE);
    spdlog::info("Test mode enabled - TestModule created");
  }

  // 初始化测试模块（如果启用）
  if (test_module) {
    test_module->init();
  }

  // 设置模块间依赖关系
  // 设置测试模块的GPIO管理器（如果启用）
  if (test_module) {
    test_module->set_gpio_manager(gpio_manager);
  }

  // 启动测试模块（如果启用）
  if (test_module) {
    test_module->start();
    spdlog::info("Test module started - GPIO trigger testing enabled");
  }
  
  spdlog::info("aby_box started");

  while (true) {
    if (bExit) {
      wifi.stop();  // 单例使用点操作符
      box_detector->stop();
      weighing->stop();
      video_engine->stop();
      weight_analyzer->stop();
      stats->stop();
      cloud_sync_manager->stop();
      image_capture_manager->stop();
      bluetooth_manager->stop();
      initialization_manager.stop();
      gpio_manager->stop();
      ota_manager->stop();
      lighting->stop();
      
      // 停止测试模块（如果启用）
      if (test_module) {
        test_module->stop();
      }
      wifi.join();  // 单例使用点操作符
      box_detector->join();
      weighing->join();
      video_engine->join();
      weight_analyzer->join();
      stats->join();
      cloud_sync_manager->join();
      image_capture_manager->join();
      bluetooth_manager->join();
      initialization_manager.join();
      gpio_manager->join();
      ota_manager->join();
      lighting->join();
      
      // 等待测试模块结束（如果启用）
      if (test_module) {
        test_module->join();
      }
      aby_box::APIClient::getInstance().cleanup();
      break;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  }
  std::cout << "exit safely" << std::endl;
  return 0;
}
